import { createSignal, Show } from 'solid-js';
import { FolderO<PERSON>, ArrowRight } from 'lucide-solid';
import Modal from '../ui/Modal';
import { Button, toast } from '../ui';
import { usePaperContext } from '../../stores/PaperContext';
import { formatFolderPath } from '../../utils/folderUtils';
import FolderSelector from './FolderSelector';

const MovePapersModal = (props) => {
  const { folderTree, updatePaper, loadFolderTree, loadPapers } = usePaperContext();
  
  const [selectedFolder, setSelectedFolder] = createSignal('');
  const [isMoving, setIsMoving] = createSignal(false);

  // 处理文件夹选择
  const handleFolderSelect = (folderPath) => {
    setSelectedFolder(folderPath);
  };

  // 处理移动操作
  const handleMove = async () => {
    if (!props.selectedPapers || props.selectedPapers.length === 0) {
      toast.error('请选择要移动的文献');
      return;
    }

    try {
      setIsMoving(true);
      
      const targetFolder = selectedFolder();
      const paperIds = props.selectedPapers;
      
      // 批量更新文献的文件夹路径
      await Promise.all(
        paperIds.map(paperId => 
          updatePaper(paperId, { folder_path: targetFolder || undefined })
        )
      );

      // 重新加载数据
      await Promise.all([
        loadPapers(),
        loadFolderTree()
      ]);

      // 显示成功消息
      const folderName = formatFolderPath(targetFolder);
      toast.success(`已成功移动 ${paperIds.length} 篇文献到 ${folderName}`);
      
      // 关闭对话框
      handleClose();
      
      // 通知父组件
      if (props.onSuccess) {
        props.onSuccess();
      }
    } catch (err) {
      console.error('移动文献失败:', err);
      toast.error(err.message || '移动文献失败，请重试');
    } finally {
      setIsMoving(false);
    }
  };

  // 关闭对话框
  const handleClose = () => {
    if (isMoving()) return;
    
    setSelectedFolder('');
    setIsMoving(false);
    
    if (props.onClose) {
      props.onClose();
    }
  };

  return (
    <Modal
      open={props.open}
      onClose={handleClose}
      title="移动文献"
      size="md"
      disableBackdropClose={isMoving()}
      showCloseButton={!isMoving()}
    >
      <div class="space-y-6">
        {/* 移动信息 */}
        <div class="bg-theme-muted p-4 rounded-lg border border-theme-primary">
          <div class="flex items-center space-x-2 mb-2">
            <FolderOpen size={16} class="text-theme-muted" />
            <span class="text-sm font-medium text-theme-secondary">移动操作</span>
          </div>
          <div class="flex items-center space-x-3 text-sm">
            <span class="text-theme-primary font-medium">
              {props.selectedPapers?.length || 0} 篇文献
            </span>
            <ArrowRight size={14} class="text-theme-muted" />
            <span class="text-theme-primary font-medium">
              {formatFolderPath(selectedFolder())}
            </span>
          </div>
          <p class="text-xs text-theme-muted mt-2">
            选择目标文件夹，文献将被移动到该文件夹中
          </p>
        </div>

        {/* 文件夹选择器 */}
        <div class="space-y-3">
          <label class="block text-sm font-medium text-theme-primary">
            选择目标文件夹
          </label>
          <div class="max-h-64 overflow-y-auto border border-theme-primary rounded-lg p-3 bg-theme-elevated">
            <FolderSelector
              folderTree={folderTree()}
              selectedFolder={selectedFolder()}
              onSelect={handleFolderSelect}
            />
          </div>
        </div>

        {/* 按钮组 */}
        <div class="flex items-center justify-end space-x-3 pt-4 border-t border-theme-primary">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isMoving()}
          >
            取消
          </Button>
          <Button
            type="button"
            loading={isMoving()}
            disabled={isMoving() || !props.selectedPapers?.length}
            onClick={handleMove}
          >
            移动文献
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default MovePapersModal;
