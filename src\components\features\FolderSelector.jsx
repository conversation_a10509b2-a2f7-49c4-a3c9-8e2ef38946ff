import { createSignal, For, createMemo } from 'solid-js';
import { Folder, FolderO<PERSON>, ChevronRight, ChevronDown } from 'lucide-solid';

const FolderSelector = (props) => {
  const [expandedFolders, setExpandedFolders] = createSignal(new Set());
  
  // 切换文件夹展开状态
  const toggleFolder = (folderPath) => {
    const expanded = new Set(expandedFolders());
    if (expanded.has(folderPath)) {
      expanded.delete(folderPath);
    } else {
      expanded.add(folderPath);
    }
    setExpandedFolders(expanded);
  };

  // 选择文件夹
  const selectFolder = (folderPath) => {
    if (props.onSelect) {
      props.onSelect(folderPath);
    }
  };

  // 递归文件夹项组件
  const FolderItem = (folderProps) => {
    const depth = folderProps.depth || 0;
    const maxDepth = 5;

    const isExpanded = createMemo(() => expandedFolders().has(folderProps.folder.path));
    const isSelected = createMemo(() => props.selectedFolder === folderProps.folder.path);
    const hasChildren = createMemo(() => folderProps.folder.subfolders && folderProps.folder.subfolders.length > 0);

    return (
      <div class="mb-1">
        <div
          class={`flex items-center px-3 py-2 rounded-lg cursor-pointer transition-colors group relative ${
            isSelected()
              ? 'bg-theme-accent text-theme-accent border border-theme-accent'
              : 'hover:bg-theme-muted text-theme-secondary hover:text-theme-primary'
          }`}
          onClick={() => selectFolder(folderProps.folder.path)}
          style={{ 'margin-left': `${depth * 16}px` }}
        >
          {/* 文件夹图标 */}
          {isExpanded() && hasChildren() ? (
            <FolderOpen size={16} class={`mr-2 ${isSelected() ? 'text-theme-accent' : 'text-theme-muted'}`} />
          ) : (
            <Folder size={16} class={`mr-2 ${isSelected() ? 'text-theme-accent' : 'text-theme-muted'}`} />
          )}

          {/* 文件夹名称 */}
          <span class="flex-1 text-sm truncate font-medium">
            {folderProps.folder.name}
          </span>

          {/* 文献数量 */}
          <div class="flex items-center space-x-2">
            {folderProps.folder.papers_count > 0 && (
              <span class="inline-flex items-center justify-center min-w-[18px] h-[18px] px-1 bg-theme-muted text-theme-primary text-xs font-medium rounded-full">
                {folderProps.folder.papers_count}
              </span>
            )}

            {/* 展开/收起按钮 */}
            {hasChildren() && (
              <button
                class="p-1 rounded hover:bg-theme-muted transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFolder(folderProps.folder.path);
                }}
                title={isExpanded() ? '收起文件夹' : '展开文件夹'}
              >
                {isExpanded() ? (
                  <ChevronDown size={14} class="text-theme-muted" />
                ) : (
                  <ChevronRight size={14} class="text-theme-muted" />
                )}
              </button>
            )}
          </div>
        </div>

        {/* 子文件夹 */}
        {hasChildren() && isExpanded() && depth < maxDepth && (
          <div class="mt-1 space-y-1">
            <For each={folderProps.folder.subfolders}>
              {(child) => (
                <FolderItem
                  folder={child}
                  depth={depth + 1}
                />
              )}
            </For>
          </div>
        )}
      </div>
    );
  };

  return (
    <div class="space-y-1">
      {/* 根目录选项 */}
      <div
        class={`flex items-center px-3 py-2 rounded-lg cursor-pointer transition-colors ${
          props.selectedFolder === ''
            ? 'bg-theme-accent text-theme-accent border border-theme-accent'
            : 'hover:bg-theme-muted text-theme-secondary hover:text-theme-primary'
        }`}
        onClick={() => selectFolder('')}
      >
        <Folder size={16} class="mr-2 text-theme-muted" />
        <span class="flex-1 text-sm font-medium">根目录</span>
      </div>

      {/* 文件夹树 */}
      <For each={props.folderTree || []}>
        {(folder) => <FolderItem folder={folder} depth={0} />}
      </For>
    </div>
  );
};

export default FolderSelector;
