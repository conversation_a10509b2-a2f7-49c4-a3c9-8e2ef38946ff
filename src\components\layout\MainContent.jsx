import { createEffect } from 'solid-js';
import { usePaperContext } from '../../stores/PaperContext';
import PaperGrid from '../features/PaperGrid';
import PaperToolbar from '../features/PaperToolbar';
import PaperBatchActions from '../features/PaperBatchActions';

const MainContent = (props) => {
  // 使用PaperContext
  const {
    papers,
    loading,
    error,
    selectedPapers,
    selectMode,
    filters,
    setSearch,
    updateFilters,
    togglePaperSelection,
    clearSelection,
    setSelectMode,
    loadPapers
  } = usePaperContext();

  // 监听全局搜索查询变化
  createEffect((prev) => {
    const currentQuery = props.globalSearchQuery || '';

    // 如果是第一次运行，不执行搜索
    if (prev === undefined) {
      return currentQuery;
    }

    // 只有当查询真正变化时才重新搜索
    if (prev !== currentQuery) {
      setSearch(currentQuery);
    }

    return currentQuery;
  });

  // 切换选择模式
  const toggleSelectMode = () => {
    setSelectMode(!selectMode());
    if (!selectMode()) {
      clearSelection();
    }
  };

  // 处理排序变化
  const handleSortChange = (sortBy) => {
    updateFilters({ sort_by: sortBy });
  };

  // 处理排序顺序变化
  const handleSortOrderToggle = () => {
    const newOrder = filters().sort_order === 'asc' ? 'desc' : 'asc';
    updateFilters({ sort_order: newOrder });
  };

  // 处理年份筛选
  const handleYearStartChange = (value) => {
    const yearStart = value ? parseInt(value) : null;
    updateFilters({ year_start: yearStart });
  };

  const handleYearEndChange = (value) => {
    const yearEnd = value ? parseInt(value) : null;
    updateFilters({ year_end: yearEnd });
  };

  const handleClearYearFilter = () => {
    updateFilters({ year_start: null, year_end: null });
  };



  return (
    <main class={`flex-1 flex flex-col bg-theme-secondary transition-all duration-300 ${
      props.drawerOpen ? `mr-${Math.floor(props.drawerWidth / 16)}` : ''
    }`}>
      {/* 批量操作栏 */}
      <PaperBatchActions
        selectedCount={selectedPapers().size}
        onClearSelection={clearSelection}
      />

      {/* 工具栏 */}
      <PaperToolbar
        sortBy={filters().sort_by}
        sortOrder={filters().sort_order}
        selectMode={selectMode()}
        yearStart={filters().year_start}
        yearEnd={filters().year_end}
        onSortByChange={handleSortChange}
        onSortOrderToggle={handleSortOrderToggle}
        onSelectModeToggle={toggleSelectMode}
        onYearStartChange={handleYearStartChange}
        onYearEndChange={handleYearEndChange}
        onClearYearFilter={handleClearYearFilter}
      />

      {/* 内容区域 */}
      <div class="flex-1 overflow-y-auto p-6">
        <PaperGrid
          papers={papers()}
          loading={loading()}
          error={error()}
          selectedPapers={selectedPapers()}
          selectMode={selectMode()}
          onPaperSelect={props.onPaperSelect}
          onToggleSelection={togglePaperSelection}
          onRetry={() => loadPapers()}
        />
      </div>
    </main>
  );
};

export default MainContent;
