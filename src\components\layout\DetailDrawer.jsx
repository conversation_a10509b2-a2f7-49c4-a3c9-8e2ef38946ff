import { createSignal, createEffect, Show, For } from 'solid-js';
import {
  X,
  Edit,
  Save,
  Upload,
  Download,
  FileText,
  Image,
  File,
  Calendar,
  User,
  BookOpen,
  Tag,
  Folder,
  Plus,
  Eye
} from 'lucide-solid';
import { formatFolderPath } from '../../utils/folderUtils';
import { Button, Card, Input, Textarea, Badge, FileUpload, NumberInput, ImagePreview, toast } from '../ui';
import { filesAPI } from '../../services/api';
import { usePaperContext } from '../../stores/PaperContext';
import PdfSplitViewModal from '../features/PdfSplitViewModal';
import FolderSelector from '../features/FolderSelector';

const DetailDrawer = (props) => {
  // 使用 PaperContext
  const { updatePaper, folderTree } = usePaperContext();

  const [isEditing, setIsEditing] = createSignal(false);
  const [editData, setEditData] = createSignal({});
  const [isDragging, setIsDragging] = createSignal(false);
  const [showFileUpload, setShowFileUpload] = createSignal(false);
  const [isUploading, setIsUploading] = createSignal(false);
  const [isSaving, setIsSaving] = createSignal(false);
  const [selectedFileType, setSelectedFileType] = createSignal('note');
  const [authorInput, setAuthorInput] = createSignal('');
  const [keywordInput, setKeywordInput] = createSignal('');

  // 图片预览状态
  const [showImagePreview, setShowImagePreview] = createSignal(false);
  const [previewImages, setPreviewImages] = createSignal([]);
  const [currentImageIndex, setCurrentImageIndex] = createSignal(0);

  // PDF预览状态
  const [showPdfPreview, setShowPdfPreview] = createSignal(false);
  const [currentPdfFile, setCurrentPdfFile] = createSignal(null);

  // 当选中文献变化时，重置编辑状态
  createEffect(() => {
    if (props.paper) {
      setEditData({
        ...props.paper,
        folder_path: props.paper.folder_path || '' // 确保 folder_path 有默认值
      });
      setIsEditing(false);
    }
  });

  // 开始拖拽调整宽度
  const startDrag = (e) => {
    e.preventDefault();
    setIsDragging(true);
    const startX = e.clientX;
    const startWidth = props.width;

    const handleMouseMove = (e) => {
      const deltaX = startX - e.clientX;
      const newWidth = Math.max(300, Math.min(1200, startWidth + deltaX));
      props.onWidthChange(newWidth);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // 保存编辑
  const handleSave = async () => {
    if (!props.paper?.id) {
      toast.error('文献ID不存在，无法保存');
      return;
    }

    try {
      setIsSaving(true);

      // 数据验证
      const data = editData();
      if (!data.title?.trim()) {
        toast.error('标题不能为空');
        return;
      }

      if (!data.journal?.trim()) {
        toast.error('期刊不能为空');
        return;
      }

      if (!data.year || data.year < 1900 || data.year > new Date().getFullYear() + 10) {
        toast.error('请输入有效的年份');
        return;
      }

      // 清理和格式化数据
      const cleanData = {
        title: data.title.trim(),
        authors: data.authors || [],
        journal: data.journal.trim(),
        year: parseInt(data.year),
        doi: data.doi?.trim() || undefined,
        abstract_text: data.abstract_text?.trim() || undefined,
        keywords: data.keywords || [],
        folder_path: data.folder_path !== null ? data.folder_path : undefined
      };

      console.log('保存文献数据:', cleanData);

      // 调用API更新文献
      const updatedPaper = await updatePaper(props.paper.id, cleanData);

      // 更新本地编辑数据
      setEditData(updatedPaper);

      // 退出编辑模式
      setIsEditing(false);

      // 显示成功消息
      toast.success('文献信息已成功保存');

      // 通知父组件文献已更新
      if (props.onPaperUpdate) {
        props.onPaperUpdate(props.paper.id);
      }

    } catch (error) {
      console.error('保存文献失败:', error);
      toast.error(error.message || '保存文献失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  // 取消编辑
  const handleCancel = () => {
    setEditData({
      ...props.paper,
      folder_path: props.paper.folder_path || '' // 重置文件夹路径
    });
    setIsEditing(false);
  };

  // 添加作者
  const addAuthor = () => {
    const authorName = authorInput().trim();
    if (!authorName) return;

    const currentAuthors = editData().authors || [];
    if (currentAuthors.includes(authorName)) {
      console.warn('该作者已存在');
      return;
    }

    setEditData(prev => ({
      ...prev,
      authors: [...currentAuthors, authorName]
    }));
    setAuthorInput('');
  };

  // 移除作者
  const removeAuthor = (authorName) => {
    setEditData(prev => ({
      ...prev,
      authors: (prev.authors || []).filter(author => author !== authorName)
    }));
  };

  // 处理作者输入框回车事件
  const handleAuthorKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addAuthor();
    }
  };

  // 添加关键词
  const addKeyword = () => {
    const keywordText = keywordInput().trim();
    if (!keywordText) return;

    const currentKeywords = editData().keywords || [];
    if (currentKeywords.includes(keywordText)) {
      console.warn('该关键词已存在');
      return;
    }

    setEditData(prev => ({
      ...prev,
      keywords: [...currentKeywords, keywordText]
    }));
    setKeywordInput('');
  };

  // 移除关键词
  const removeKeyword = (keywordText) => {
    setEditData(prev => ({
      ...prev,
      keywords: (prev.keywords || []).filter(keyword => keyword !== keywordText)
    }));
  };

  // 处理关键词输入框回车事件
  const handleKeywordKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addKeyword();
    }
  };

  // 检查是否已有原文文件
  const hasOriginFile = () => {
    return props.paper?.files?.some(file => file.file_type === 'Origin') || false;
  };

  // 文件上传处理（选择后自动上传）
  const handleFileUpload = async (files) => {
    if (files.length === 0) return;

    // 原文文件覆盖提示
    if (selectedFileType() === 'origin' && hasOriginFile()) {
      toast.info('检测到已有原文文件，新上传的文件将覆盖现有原文');
    }

    try {
      setIsUploading(true);
      console.log('开始上传文件:', files, '类型:', selectedFileType());

      // 逐个上传文件
      for (const fileItem of files) {
        try {
          // 从fileItem中提取真实的File对象
          const actualFile = fileItem.file;
          const response = await filesAPI.upload(props.paper.id, actualFile, selectedFileType());
          console.log('文件上传成功:', response.data);

          // 显示成功提示
          toast.success(`文件 "${fileItem.name}" 上传成功`);
        } catch (error) {
          console.error('文件上传失败:', fileItem.name, error);
          toast.error(`文件 "${fileItem.name}" 上传失败: ${error.message}`);
        }
      }

      // 上传完成后刷新文件列表（不关闭上传组件）
      // 触发父组件刷新文献数据
      if (props.onPaperUpdate) {
        props.onPaperUpdate(props.paper.id);
      }

    } catch (error) {
      console.error('文件上传过程出错:', error);
      toast.error('文件上传失败');
    } finally {
      setIsUploading(false);
    }
  };

  // 文件下载处理
  const handleFileDownload = async (file) => {
    try {
      console.log('开始下载文件:', file.name);

      const response = await filesAPI.download(props.paper.id, file.name);

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', file.name);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast.success(`文件 "${file.name}" 下载成功`);
    } catch (error) {
      console.error('文件下载失败:', error);
      toast.error(`文件下载失败: ${error.message}`);
    }
  };

  // 图片预览处理
  const handleImagePreview = (file, imageFiles) => {
    // 构建图片列表，包含预览URL
    const images = imageFiles.map(imgFile => ({
      src: `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${props.paper.id}/files/${imgFile.name}`,
      name: imgFile.name,
      alt: imgFile.name,
      file: imgFile
    }));

    // 找到当前点击图片的索引
    const currentIndex = imageFiles.findIndex(imgFile => imgFile.name === file.name);

    setPreviewImages(images);
    setCurrentImageIndex(currentIndex >= 0 ? currentIndex : 0);
    setShowImagePreview(true);
  };

  // 图片下载处理（从预览组件调用）
  const handleImageDownload = (image) => {
    if (image.file) {
      handleFileDownload(image.file);
    }
  };

  // PDF预览处理
  const handlePdfPreview = (file) => {
    setCurrentPdfFile(file);
    setShowPdfPreview(true);
  };

  // 获取分类文件
  const getFilesByType = () => {
    const files = props.paper?.files || [];
    return {
      originFiles: files.filter(file => file.file_type === 'Origin'),
      noteFiles: files.filter(file => file.file_type === 'Note'),
      imageFiles: files.filter(file => file.file_type === 'Image')
    };
  };



  return (
    <Show when={props.open}>
      {/* 透明遮罩 */}
      <div
        class="fixed inset-0 z-40 transition-opacity duration-300 modal-overlay-theme"
        onClick={props.onClose}
      />

      {/* 抽屉主体 */}
      <div
        class={`fixed right-0 top-0 h-full bg-theme-primary border-l border-theme-primary shadow-theme-lg z-50 transition-transform duration-300 flex flex-col ${
          props.open ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{ width: `${props.width}px` }}
      >
        {/* 拖拽调整手柄 */}
        <div
          class="absolute left-0 top-0 w-1 h-full cursor-col-resize hover:bg-theme-accent transition-colors bg-theme-secondary"
          onMouseDown={startDrag}
          title="拖拽调整宽度"
        />

        {/* 抽屉头部 */}
        <div class="flex items-center justify-between p-6 border-b border-theme-muted bg-gradient-to-r from-theme-muted to-theme-primary flex-shrink-0">
          <div>
            <h2 class="text-xl font-bold text-theme-primary">文献详情</h2>
            <p class="text-sm text-theme-muted mt-1">Paper Details</p>
          </div>
          <div class="flex items-center space-x-2">
            {isEditing() ? (
              <>
                <Button
                  onClick={handleSave}
                  variant="success"
                  size="sm"
                  title="保存"
                  loading={isSaving()}
                  disabled={isSaving()}
                >
                  <Save size={16} />
                  {isSaving() ? '保存中...' : '保存'}
                </Button>
                <Button
                  onClick={handleCancel}
                  variant="outline"
                  size="sm"
                  title="取消"
                  disabled={isSaving()}
                >
                  <X size={16} />
                  取消
                </Button>
              </>
            ) : (
              <Button
                onClick={() => setIsEditing(true)}
                variant="outline"
                size="sm"
                title="编辑"
              >
                <Edit size={16} />
                编辑
              </Button>
            )}
            <Button
              onClick={props.onClose}
              variant="ghost"
              size="sm"
              title="关闭"
            >
              <X size={20} />
            </Button>
          </div>
        </div>

        {/* 抽屉内容 */}
        <div class="flex-1 overflow-y-auto p-6 space-y-8 min-h-0">
          <Show when={props.paper}>
            {/* 基本信息 */}
            <Card
              variant="elevated"
              header={
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-6 bg-gradient-to-b from-blue-500 to-blue-600 rounded-full"></div>
                  <h3 class="text-lg font-semibold text-theme-primary">基本信息</h3>
                  <span class="text-sm text-theme-muted">Basic Information</span>
                </div>
              }
            >
              <div class="space-y-6">
              
              {/* 标题 */}
              <div class="space-y-3">
                <label class="flex items-center space-x-2 text-sm font-semibold text-theme-primary">
                  <BookOpen size={16} class="text-blue-500" />
                  <span>标题</span>
                </label>
                {isEditing() ? (
                  <Textarea
                    value={editData().title || ''}
                    onInput={(e) => setEditData({ ...editData(), title: e.target.value })}
                    rows="3"
                    placeholder="请输入文献标题..."
                    resize="none"
                    autoResize={true}
                  />
                ) : (
                  <div class="bg-theme-muted rounded-lg p-4 border-l-4 border-blue-500">
                    <p class="text-theme-primary leading-relaxed font-medium text-lg">{props.paper.title}</p>
                  </div>
                )}
              </div>

              {/* 作者 */}
              <div class="space-y-3">
                <label class="flex items-center space-x-2 text-sm font-semibold text-theme-primary">
                  <User size={16} class="text-green-500" />
                  <span>作者</span>
                </label>
                {isEditing() ? (
                  <div class="space-y-3">
                    {/* 作者输入框 */}
                    <div class="flex items-center space-x-2">
                      <Input
                        value={authorInput()}
                        onInput={(e) => setAuthorInput(e.target.value)}
                        onKeyPress={handleAuthorKeyPress}
                        placeholder="输入作者姓名"
                        leftIcon={<User size={16} />}
                        class="flex-1"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addAuthor}
                        disabled={!authorInput().trim()}
                      >
                        <Plus size={16} />
                        添加
                      </Button>
                    </div>

                    {/* 作者标签列表 */}
                    <Show when={(editData().authors || []).length > 0}>
                      <div class="flex flex-wrap gap-2 p-3 bg-theme-muted rounded-lg border border-theme-primary min-h-[60px]">
                        <For each={editData().authors || []}>
                          {(author) => (
                            <div class="inline-flex items-center px-3 py-1 bg-theme-elevated border border-theme-secondary rounded-full text-sm">
                              <span class="text-theme-primary">{author}</span>
                              <button
                                type="button"
                                onClick={() => removeAuthor(author)}
                                class="ml-2 text-theme-muted hover:text-theme-error focus:outline-none"
                              >
                                <X size={14} />
                              </button>
                            </div>
                          )}
                        </For>
                      </div>
                    </Show>

                    {/* 空状态提示 */}
                    <Show when={(editData().authors || []).length === 0}>
                      <div class="p-3 bg-theme-muted rounded-lg border border-theme-primary min-h-[60px] flex items-center justify-center">
                        <span class="text-theme-muted text-sm">暂无作者，请在上方输入框中添加</span>
                      </div>
                    </Show>
                  </div>
                ) : (
                  <div class="flex flex-wrap gap-2">
                    {props.paper.authors?.map((author, index) => (
                      <Badge
                        key={index}
                        variant="success"
                        size="md"
                      >
                        <User size={12} class="mr-1" />
                        {author}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>

              {/* 期刊和年份 */}
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-3">
                  <label class="flex items-center space-x-2 text-sm font-semibold text-theme-primary">
                    <BookOpen size={16} class="text-purple-500" />
                    <span>期刊</span>
                  </label>
                  {isEditing() ? (
                    <Input
                      type="text"
                      value={editData().journal || ''}
                      onInput={(e) => setEditData({ ...editData(), journal: e.target.value })}
                      placeholder="请输入期刊名称"
                      leftIcon={<BookOpen size={16} />}
                    />
                  ) : (
                    <div class="bg-theme-muted rounded-lg p-3 border border-theme-secondary min-h-[48px] flex items-center">
                      <span class="text-theme-primary font-medium">{props.paper.journal}</span>
                    </div>
                  )}
                </div>
                <div class="space-y-3">
                  <label class="flex items-center space-x-2 text-sm font-semibold text-theme-primary">
                    <Calendar size={16} class="text-orange-500" />
                    <span>年份</span>
                  </label>
                  {isEditing() ? (
                    <NumberInput
                      value={editData().year || null}
                      onValueChange={(value) => setEditData({ ...editData(), year: value })}
                      placeholder="发表年份"
                      leftIcon={<Calendar size={16} />}
                      min={1900}
                      max={new Date().getFullYear() + 10}
                      step={1}
                      allowEmpty={false}
                    />
                  ) : (
                    <div class="bg-theme-muted rounded-lg p-3 border border-theme-secondary min-h-[48px] flex items-center">
                      <span class="text-theme-primary font-bold text-lg">{props.paper.year}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* DOI */}
              <div class="space-y-3">
                <label class="flex items-center space-x-2 text-sm font-semibold text-theme-primary">
                  <BookOpen size={16} class="text-cyan-500" />
                  <span>DOI</span>
                </label>
                {isEditing() ? (
                  <Input
                    type="text"
                    value={editData().doi || ''}
                    onInput={(e) => setEditData({ ...editData(), doi: e.target.value })}
                    placeholder="请输入DOI（可选）"
                    leftIcon={<BookOpen size={16} />}
                  />
                ) : (
                  <div class="bg-theme-muted rounded-lg p-3 border border-theme-secondary min-h-[48px] flex items-center">
                    <span class="text-theme-primary font-medium">
                      {props.paper.doi || '未设置'}
                    </span>
                  </div>
                )}
              </div>

              {/* 文件夹 */}
              <div class="space-y-3">
                <label class="flex items-center space-x-2 text-sm font-semibold text-theme-primary">
                  <Folder size={16} class="text-yellow-500" />
                  <span>文件夹</span>
                </label>
                {isEditing() ? (
                  <div class="space-y-3">
                    <div class="bg-theme-muted p-3 rounded-lg border border-theme-primary">
                      <div class="flex items-center space-x-2 mb-2">
                        <Folder size={14} class="text-theme-muted" />
                        <span class="text-xs text-theme-secondary font-medium">当前选择</span>
                      </div>
                      <div class="text-sm text-theme-primary font-medium">
                        {formatFolderPath(editData().folder_path)}
                      </div>
                    </div>
                    <div class="max-h-48 overflow-y-auto border border-theme-primary rounded-lg p-3 bg-theme-elevated">
                      <FolderSelector
                        folderTree={folderTree()}
                        selectedFolder={editData().folder_path || ''}
                        onSelect={(folderPath) => setEditData({ ...editData(), folder_path: folderPath })}
                      />
                    </div>
                  </div>
                ) : (
                  <div class="bg-theme-muted rounded-lg p-3 border border-theme-secondary">
                    <span class="text-theme-primary font-medium">{formatFolderPath(props.paper.folder_path)}</span>
                  </div>
                )}
              </div>

              {/* 关键词 */}
              <div class="space-y-3">
                <label class="flex items-center space-x-2 text-sm font-semibold text-theme-primary">
                  <Tag size={16} class="text-pink-500" />
                  <span>关键词</span>
                </label>
                {isEditing() ? (
                  <div class="space-y-3">
                    {/* 关键词输入框 */}
                    <div class="flex items-center space-x-2">
                      <Input
                        value={keywordInput()}
                        onInput={(e) => setKeywordInput(e.target.value)}
                        onKeyPress={handleKeywordKeyPress}
                        placeholder="输入关键词"
                        leftIcon={<Tag size={16} />}
                        class="flex-1"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addKeyword}
                        disabled={!keywordInput().trim()}
                      >
                        <Plus size={16} />
                        添加
                      </Button>
                    </div>

                    {/* 关键词标签列表 */}
                    <Show when={(editData().keywords || []).length > 0}>
                      <div class="flex flex-wrap gap-2 p-3 bg-theme-muted rounded-lg border border-theme-primary min-h-[60px]">
                        <For each={editData().keywords || []}>
                          {(keyword) => (
                            <div class="inline-flex items-center px-3 py-1 bg-theme-elevated border border-theme-secondary rounded-full text-sm">
                              <span class="text-theme-primary">{keyword}</span>
                              <button
                                type="button"
                                onClick={() => removeKeyword(keyword)}
                                class="ml-2 text-theme-muted hover:text-theme-error focus:outline-none"
                              >
                                <X size={14} />
                              </button>
                            </div>
                          )}
                        </For>
                      </div>
                    </Show>

                    {/* 空状态提示 */}
                    <Show when={(editData().keywords || []).length === 0}>
                      <div class="p-3 bg-theme-muted rounded-lg border border-theme-primary min-h-[60px] flex items-center justify-center">
                        <span class="text-theme-muted text-sm">暂无关键词，请在上方输入框中添加</span>
                      </div>
                    </Show>
                  </div>
                ) : (
                  <div class="flex flex-wrap gap-2">
                    {props.paper.keywords?.map(keyword => (
                      <Badge
                        key={keyword}
                        variant="info"
                        size="md"
                      >
                        <Tag size={12} class="mr-1" />
                        {keyword}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
              </div>
            </Card>

            {/* 摘要 */}
            {(props.paper.abstract_text || isEditing()) && (
              <Card
                variant="elevated"
                header={
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-6 bg-gradient-to-b from-indigo-500 to-indigo-600 rounded-full"></div>
                    <h3 class="text-lg font-semibold text-theme-primary">摘要</h3>
                    <span class="text-sm text-theme-muted">Abstract</span>
                  </div>
                }
              >
                {isEditing() ? (
                  <Textarea
                    value={editData().abstract_text || ''}
                    onInput={(e) => setEditData({ ...editData(), abstract_text: e.target.value })}
                    rows="8"
                    placeholder="输入摘要内容..."
                    resize="vertical"
                    size="lg"
                  />
                ) : (
                  <div class="bg-theme-muted rounded-lg p-5 border border-theme-secondary">
                    <p class="text-theme-primary leading-relaxed text-base">{props.paper.abstract_text}</p>
                  </div>
                )}
              </Card>
            )}

            {/* 文件管理 */}
            <Card
              variant="elevated"
              header={
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-6 bg-gradient-to-b from-emerald-500 to-emerald-600 rounded-full"></div>
                    <h3 class="text-lg font-semibold text-theme-primary">文件</h3>
                    <span class="text-sm text-theme-muted">Attachments</span>
                  </div>
                  <Button
                    variant="success"
                    size="sm"
                    onClick={() => setShowFileUpload(!showFileUpload())}
                  >
                    <Upload size={16} />
                    {showFileUpload() ? '取消上传' : '上传文件'}
                  </Button>
                </div>
              }
            >

              {/* 文件上传组件 */}
              {showFileUpload() && (
                <div class="space-y-4">
                  {/* 文件类型选择卡片 */}
                  <div class="bg-theme-muted p-4 rounded-lg border border-theme-primary">
                    <label class="block text-sm font-medium text-theme-primary mb-3">
                      选择文件类型
                    </label>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                      {/* 原文文件卡片 */}
                      <div
                        class={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                          selectedFileType() === 'origin'
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-md'
                            : 'border-theme-secondary bg-theme-elevated hover:border-blue-300 hover:bg-blue-50/50 dark:hover:bg-blue-900/10'
                        }`}
                        onClick={() => setSelectedFileType('origin')}
                      >
                        <div class="flex items-center space-x-3">
                          <div class={`p-2 rounded-lg ${
                            selectedFileType() === 'origin'
                              ? 'bg-blue-100 dark:bg-blue-800'
                              : 'bg-theme-muted'
                          }`}>
                            <FileText size={20} class={
                              selectedFileType() === 'origin'
                                ? 'text-blue-600 dark:text-blue-300'
                                : 'text-theme-muted'
                            } />
                          </div>
                          <div class="flex-1">
                            <h4 class={`text-sm font-semibold ${
                              selectedFileType() === 'origin'
                                ? 'text-blue-700 dark:text-blue-300'
                                : 'text-theme-primary'
                            }`}>
                              原文文件
                            </h4>
                            <p class="text-xs text-theme-muted mt-1">
                              PDF文档等原始文件
                            </p>
                          </div>
                          {selectedFileType() === 'origin' && (
                            <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                              <div class="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                          )}
                        </div>

                      </div>

                      {/* 笔记文件卡片 */}
                      <div
                        class={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                          selectedFileType() === 'note'
                            ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20 shadow-md'
                            : 'border-theme-secondary bg-theme-elevated hover:border-orange-300 hover:bg-orange-50/50 dark:hover:bg-orange-900/10'
                        }`}
                        onClick={() => setSelectedFileType('note')}
                      >
                        <div class="flex items-center space-x-3">
                          <div class={`p-2 rounded-lg ${
                            selectedFileType() === 'note'
                              ? 'bg-orange-100 dark:bg-orange-800'
                              : 'bg-theme-muted'
                          }`}>
                            <File size={20} class={
                              selectedFileType() === 'note'
                                ? 'text-orange-600 dark:text-orange-300'
                                : 'text-theme-muted'
                            } />
                          </div>
                          <div class="flex-1">
                            <h4 class={`text-sm font-semibold ${
                              selectedFileType() === 'note'
                                ? 'text-orange-700 dark:text-orange-300'
                                : 'text-theme-primary'
                            }`}>
                              笔记文件
                            </h4>
                            <p class="text-xs text-theme-muted mt-1">
                              研究笔记、总结等
                            </p>
                          </div>
                          {selectedFileType() === 'note' && (
                            <div class="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center">
                              <div class="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* 图片文件卡片 */}
                      <div
                        class={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                          selectedFileType() === 'image'
                            ? 'border-green-500 bg-green-50 dark:bg-green-900/20 shadow-md'
                            : 'border-theme-secondary bg-theme-elevated hover:border-green-300 hover:bg-green-50/50 dark:hover:bg-green-900/10'
                        }`}
                        onClick={() => setSelectedFileType('image')}
                      >
                        <div class="flex items-center space-x-3">
                          <div class={`p-2 rounded-lg ${
                            selectedFileType() === 'image'
                              ? 'bg-green-100 dark:bg-green-800'
                              : 'bg-theme-muted'
                          }`}>
                            <Image size={20} class={
                              selectedFileType() === 'image'
                                ? 'text-green-600 dark:text-green-300'
                                : 'text-theme-muted'
                            } />
                          </div>
                          <div class="flex-1">
                            <h4 class={`text-sm font-semibold ${
                              selectedFileType() === 'image'
                                ? 'text-green-700 dark:text-green-300'
                                : 'text-theme-primary'
                            }`}>
                              图片文件
                            </h4>
                            <p class="text-xs text-theme-muted mt-1">
                              图表、截图等图片
                            </p>
                          </div>
                          {selectedFileType() === 'image' && (
                            <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                              <div class="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <FileUpload
                    multiple={selectedFileType() !== 'origin'}
                    accept={selectedFileType() === 'image' ? 'image/*' : 'image/*,.pdf,.doc,.docx,.txt,.md'}
                    maxSize={10 * 1024 * 1024} // 10MB
                    maxFiles={selectedFileType() === 'origin' ? 1 : 5}
                    onFileSelect={handleFileUpload}
                    dragAndDrop={true}
                    showPreview={true}
                  />

                  {/* 上传状态提示 */}
                  {isUploading() && (
                    <div class="flex items-center justify-center p-4 bg-theme-muted rounded-lg">
                      <div class="flex items-center space-x-3">
                        <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-theme-accent"></div>
                        <span class="text-sm text-theme-primary">正在上传文件...</span>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 分类文件列表 */}
              <div class="space-y-6 mt-5">
                {(() => {
                  const files = props.paper.files || [];
                  const originFiles = files.filter(f => f.file_type === 'Origin');
                  const noteFiles = files.filter(f => f.file_type === 'Note');
                  const imageFiles = files.filter(f => f.file_type === 'Image');

                  return (
                    <>
                      {/* 原文文件 */}
                      <div class="space-y-3">
                        <div class="flex items-center space-x-2">
                          <FileText size={16} class="text-theme-info" />
                          <h4 class="text-sm font-semibold text-theme-primary">原文文件</h4>
                          <Badge variant="info" size="sm">{originFiles.length}</Badge>
                        </div>
                        {originFiles.length > 0 ? (
                          <div class="space-y-2">
                            <For each={originFiles}>
                              {(file) => (
                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg border border-blue-200 dark:border-blue-700 hover:shadow-md transition-all duration-200">
                                  <div class="flex items-center space-x-3">
                                    <div class="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                                      <FileText size={16} class="text-blue-600 dark:text-blue-300" />
                                    </div>
                                    <div>
                                      <p class="text-sm font-medium text-theme-primary">{file.name}</p>
                                      <span class="text-xs text-theme-muted">
                                        {file.size ? `${Math.round(file.size / 1024)} KB` : '未知大小'}
                                      </span>
                                    </div>
                                  </div>
                                  <div class="flex items-center space-x-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handlePdfPreview(file)}
                                      title="预览PDF"
                                    >
                                      <Eye size={14} />
                                      预览
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleFileDownload(file)}
                                      title="下载文件"
                                    >
                                      <Download size={14} />
                                      下载
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </For>
                          </div>
                        ) : (
                          <div class="p-4 text-center bg-blue-50 dark:bg-blue-900/20 rounded-lg border-2 border-dashed border-blue-200 dark:border-blue-700">
                            <FileText size={24} class="mx-auto text-blue-400 mb-2" />
                            <p class="text-sm text-blue-600 dark:text-blue-300">暂无原文文件</p>
                          </div>
                        )}
                      </div>

                      {/* 笔记文件 */}
                      <div class="space-y-3">
                        <div class="flex items-center space-x-2">
                          <File size={16} class="text-orange-500" />
                          <h4 class="text-sm font-semibold text-theme-primary">笔记文件</h4>
                          <Badge variant="warning" size="sm">{noteFiles.length}</Badge>
                        </div>
                        {noteFiles.length > 0 ? (
                          <div class="space-y-2">
                            <For each={noteFiles}>
                              {(file) => (
                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg border border-orange-200 dark:border-orange-700 hover:shadow-md transition-all duration-200">
                                  <div class="flex items-center space-x-3">
                                    <div class="p-2 bg-orange-100 dark:bg-orange-800 rounded-lg">
                                      <File size={16} class="text-orange-600 dark:text-orange-300" />
                                    </div>
                                    <div>
                                      <p class="text-sm font-medium text-theme-primary">{file.name}</p>
                                      <span class="text-xs text-theme-muted">
                                        {file.size ? `${Math.round(file.size / 1024)} KB` : '未知大小'}
                                      </span>
                                    </div>
                                  </div>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleFileDownload(file)}
                                  >
                                    <Download size={14} />
                                    下载
                                  </Button>
                                </div>
                              )}
                            </For>
                          </div>
                        ) : (
                          <div class="p-4 text-center bg-orange-50 dark:bg-orange-900/20 rounded-lg border-2 border-dashed border-orange-200 dark:border-orange-700">
                            <File size={24} class="mx-auto text-orange-400 mb-2" />
                            <p class="text-sm text-orange-600 dark:text-orange-300">暂无笔记文件</p>
                          </div>
                        )}
                      </div>

                      {/* 图片文件 */}
                      <div class="space-y-3">
                        <div class="flex items-center space-x-2">
                          <Image size={16} class="text-theme-success" />
                          <h4 class="text-sm font-semibold text-theme-primary">图片文件</h4>
                          <Badge variant="success" size="sm">{imageFiles.length}</Badge>
                        </div>
                        {imageFiles.length > 0 ? (
                          <div class="space-y-2">
                            <For each={imageFiles}>
                              {(file) => (
                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg border border-green-200 dark:border-green-700 hover:shadow-md transition-all duration-200">
                                  <div class="flex items-center space-x-3">
                                    {/* 图片缩略图预览 */}
                                    <div
                                      class="relative w-12 h-12 bg-green-100 dark:bg-green-800 rounded-lg overflow-hidden cursor-pointer hover:ring-2 hover:ring-green-500 transition-all"
                                      onClick={() => handleImagePreview(file, imageFiles)}
                                      title="点击预览图片"
                                    >
                                      <img
                                        src={`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${props.paper.id}/files/${file.name}`}
                                        alt={file.name}
                                        class="w-full h-full object-cover"
                                        onError={(e) => {
                                          // 图片加载失败时显示图标
                                          e.target.style.display = 'none';
                                          e.target.nextElementSibling.style.display = 'flex';
                                        }}
                                      />
                                      <div class="absolute inset-0 hidden items-center justify-center">
                                        <Image size={16} class="text-green-600 dark:text-green-300" />
                                      </div>
                                    </div>
                                    <div>
                                      <p class="text-sm font-medium text-theme-primary">{file.name}</p>
                                      <span class="text-xs text-theme-muted">
                                        {file.size ? `${Math.round(file.size / 1024)} KB` : '未知大小'}
                                      </span>
                                    </div>
                                  </div>
                                  <div class="flex items-center space-x-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleFileDownload(file)}
                                      title="下载图片"
                                    >
                                      <Download size={14} />
                                      下载
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </For>
                          </div>
                        ) : (
                          <div class="p-4 text-center bg-green-50 dark:bg-green-900/20 rounded-lg border-2 border-dashed border-green-200 dark:border-green-700">
                            <Image size={24} class="mx-auto text-green-400 mb-2" />
                            <p class="text-sm text-green-600 dark:text-green-300">暂无图片文件</p>
                          </div>
                        )}
                      </div>

                      {/* 全部为空时的提示 */}
                      {files.length === 0 && (
                        <div class="text-center py-12 bg-gradient-to-br from-theme-muted to-theme-secondary rounded-lg border-2 border-dashed border-theme-secondary">
                          <div class="p-3 bg-theme-elevated rounded-full w-fit mx-auto mb-4 shadow-theme-sm">
                            <FileText size={32} class="text-theme-muted" />
                          </div>
                          <p class="text-theme-secondary font-medium">暂无文件</p>
                          <p class="text-sm text-theme-muted mt-1">点击上方按钮上传文件</p>
                        </div>
                      )}
                    </>
                  );
                })()}
              </div>
            </Card>
          </Show>
        </div>
      </div>

      {/* 图片预览组件 */}
      <ImagePreview
        open={showImagePreview()}
        onClose={() => setShowImagePreview(false)}
        images={previewImages()}
        currentIndex={currentImageIndex()}
        onIndexChange={setCurrentImageIndex}
        title="图片预览"
        showDownload={true}
        onDownload={handleImageDownload}
      />

      {/* PDF分屏预览组件 */}
      <Show when={currentPdfFile()}>
        <PdfSplitViewModal
          open={showPdfPreview()}
          onClose={() => setShowPdfPreview(false)}
          paper={props.paper}
          originFile={currentPdfFile()}
          noteFiles={getFilesByType().noteFiles}
          imageFiles={getFilesByType().imageFiles}
          onFileDownload={handleFileDownload}
          onFileCreated={() => {
            // 刷新文件列表
            if (props.onPaperUpdate) {
              props.onPaperUpdate(props.paper.id);
            }
          }}
        />
      </Show>
    </Show>
  );
};

export default DetailDrawer;
