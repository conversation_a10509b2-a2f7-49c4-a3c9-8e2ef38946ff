import { createSignal, For, createMemo } from 'solid-js';
import {
  Folder,
  FolderOpen,
  ChevronRight,
  ChevronDown,
  Plus,
  FileText,
  X,
  Edit,
  Trash2
} from 'lucide-solid';
import { usePaperContext } from '../../stores/PaperContext';
import { foldersAPI } from '../../services/api';
import CreateFolderModal from '../features/CreateFolderModal';
import { Modal, Button, toast } from '../ui';

const Sidebar = (props) => {
  // 使用PaperContext

  const {
    folderTree,
    filters,
    updateFilters,
    foldersLoading,
    loadFolderTree
  } = usePaperContext();

  const [expandedFolders, setExpandedFolders] = createSignal(new Set());
  const [showCreateModal, setShowCreateModal] = createSignal(false);
  const [createParentPath, setCreateParentPath] = createSignal('');
  const [showRenameModal, setShowRenameModal] = createSignal(false);
  const [newFolderName, setNewFolderName] = createSignal('');
  const [showDeleteModal, setShowDeleteModal] = createSignal(false);

  // 当前选中的文件夹（从filters中获取）
  const selectedFolder = () => {
    if (filters().folder != null) {
      return filters().folder;
    } else {
      console.log("filters().folder is null");
      return null;
    }
  };

  // 切换文件夹展开状态
  const toggleFolder = (folderPath) => {
    console.log('toggleFolder called with:', folderPath);
    const expanded = new Set(expandedFolders());
    console.log('Current expanded folders:', Array.from(expanded));

    if (expanded.has(folderPath)) {
      expanded.delete(folderPath);
      console.log('Collapsing folder:', folderPath);
    } else {
      expanded.add(folderPath);
      console.log('Expanding folder:', folderPath);
    }

    setExpandedFolders(expanded);
    console.log('New expanded folders:', Array.from(expanded));
  };

  // 选择文件夹并应用筛选（不再处理展开/收起）
  const selectFolder = (folder) => {
    // 如果点击的是当前选中的文件夹，则清除筛选
    if (selectedFolder() === folder.path) {
      updateFilters({ folder: null });
    } else {
      // 应用文件夹筛选
      updateFilters({ folder: folder.path });
    }
  };

  // 清除文件夹筛选
  const clearFolderFilter = () => {
    updateFilters({ folder: null });
  };

  // 打开新建文件夹对话框
  const handleCreateFolder = (parentPath = '') => {
    // 如果没有指定父路径，使用当前选中的文件夹作为父路径
    const finalParentPath = parentPath || selectedFolder() || '';
    setCreateParentPath(finalParentPath);
    setShowCreateModal(true);
  };

  // 关闭新建文件夹对话框
  const handleCloseCreateModal = () => {
    setShowCreateModal(false);
    setCreateParentPath('');
  };

  // 新建文件夹成功回调
  const handleCreateSuccess = (folderName) => {
    console.log('文件夹创建成功:', folderName);
    // 可以在这里添加成功提示
  };



  // 删除文件夹
  const handleDeleteFolder = () => {
    const folderPath = selectedFolder();
    if (!folderPath) return;
    setShowDeleteModal(true);
  };

  // 确认删除文件夹
  const handleConfirmDelete = async () => {
    const folderPath = selectedFolder();
    if (!folderPath) return;

    try {
      await foldersAPI.delete(folderPath);
      // 清除当前选中的文件夹筛选
      updateFilters({ folder: '' });
      // 重新加载文件夹树
      await loadFolderTree();
      console.log('文件夹删除成功:', folderPath);
      toast.success('文件夹删除成功');
      setShowDeleteModal(false);
    } catch (error) {
      console.error('删除文件夹失败:', error);
      toast.error('删除文件夹失败，请重试');
      setShowDeleteModal(false);
    }
  };

  // 开始重命名文件夹
  const handleStartRename = () => {
    const folderPath = selectedFolder();
    if (!folderPath) return;

    // 获取当前文件夹名称（路径的最后一部分）
    const currentName = folderPath.split('/').pop();
    setNewFolderName(currentName);
    setShowRenameModal(true);
  };

  // 确认重命名文件夹
  const handleConfirmRename = async () => {
    const folderPath = selectedFolder();
    const newName = newFolderName().trim();

    if (!folderPath || !newName) return;

    try {
      await foldersAPI.rename(folderPath, newName);

      // 关闭重命名弹窗
      setShowRenameModal(false);
      setNewFolderName('');

      // 计算新的文件夹路径
      const pathParts = folderPath.split('/');
      pathParts[pathParts.length - 1] = newName;
      const newFolderPath = pathParts.join('/');

      // 重新加载文件夹树
      await loadFolderTree();

      // 更新选中的文件夹为新路径，保持选中状态
      updateFilters({ folder: newFolderPath });

      console.log('文件夹重命名成功:', folderPath, '->', newFolderPath);
      toast.success('文件夹重命名成功');
    } catch (error) {
      console.error('重命名文件夹失败:', error);
      toast.error('重命名文件夹失败，请重试');
    }
  };

  // 取消重命名
  const handleCancelRename = () => {
    setShowRenameModal(false);
    setNewFolderName('');
  };



  // 递归文件夹项组件 - 支持多层级
  const FolderItem = (props) => {
    const depth = props.depth || 0;
    const maxDepth = 5; // 最大支持5层

    const isExpanded = createMemo(() => expandedFolders().has(props.folder.path));
    const isSelected = createMemo(() => selectedFolder() === props.folder.path);
    const hasChildren = createMemo(() => props.folder.subfolders && props.folder.subfolders.length > 0);

    return (
      <div class="mb-1">
        <div
          class={`flex items-center px-3 py-2 rounded-lg cursor-pointer transition-colors group relative ${
            isSelected()
              ? 'sidebar-item-active-theme'
              : 'sidebar-item-theme hover:bg-theme-muted'
          }`}
          onClick={() => selectFolder(props.folder)}
          style={{ 'margin-left': `${depth * 16}px` }}
        >
          {/* 文件夹图标 - 选中时使用橙色 */}
          {isExpanded() && hasChildren() ? (
            <FolderOpen size={16} class={`mr-2 ${isSelected() ? 'text-orange-500' : 'text-theme-muted'}`} />
          ) : (
            <Folder size={16} class={`mr-2 ${isSelected() ? 'text-orange-500' : 'text-theme-muted'}`} />
          )}

          {/* 文件夹名称 */}
          <span class="flex-1 text-sm text-theme-primary truncate font-medium">
            {props.folder.name}
          </span>

          {/* 文献数量标识 */}
          <div class="flex items-center space-x-2">
            {/* 文献数量 - 显示为小圆点或数字 */}
            {props.folder.papers_count > 0 ? (
              <span class="inline-flex items-center justify-center min-w-[18px] h-[18px] px-1 bg-theme-muted text-theme-primary text-xs font-medium rounded-full">
                {props.folder.papers_count}
              </span>
            ) : (
              <div class="w-2 h-2 bg-theme-muted rounded-full opacity-50" title="无文献" />
            )}

            {/* 子文件夹指示器 - 三角形，根据展开状态改变方向，可点击展开/收起 */}
            {hasChildren() && (
              <button
                class="p-2 rounded hover:bg-theme-muted transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFolder(props.folder.path);
                }}
                title={isExpanded() ? '收起文件夹' : '展开文件夹'}
              >
                {isExpanded() ? (
                  <ChevronDown size={16} class="text-theme-muted flex-shrink-0" />
                ) : (
                  <ChevronRight size={16} class="text-theme-muted flex-shrink-0" />
                )}
              </button>
            )}
          </div>
        </div>

        {/* 子文件夹 - 递归渲染 */}
        {hasChildren() && isExpanded() && depth < maxDepth && (
          <div class="mt-1 space-y-1">
            <For each={props.folder.subfolders}>
              {(child) => (
                <FolderItem
                  folder={child}
                  depth={depth + 1}
                />
              )}
            </For>
          </div>
        )}
      </div>
    );
  };

  return (
    <aside class={`sidebar-theme border-r transition-all duration-300 ${
      props.collapsed ? 'w-16' : 'w-64'
    }`}>
      <div class="h-full flex flex-col">
        {/* 侧边栏头部 */}
        {!props.collapsed && (
          <div class="p-4 border-b border-theme-primary">
            <div class="flex items-center justify-between">
              <h2 class="text-sm font-semibold text-theme-primary">文件夹</h2>
              <div class="flex items-center space-x-1">
                {/* 文件夹操作按钮 - 只在选中文件夹时显示 */}
                {selectedFolder() && (
                  <>
                    <button
                      onClick={handleStartRename}
                      class="p-1 rounded hover:bg-blue-100 text-blue-600"
                      title="重命名文件夹"
                    >
                      <Edit size={14} />
                    </button>
                    <button
                      onClick={handleDeleteFolder}
                      class="p-1 rounded hover:bg-red-100 text-red-600"
                      title="删除文件夹"
                    >
                      <Trash2 size={14} />
                    </button>
                  </>
                )}

                {/* 清除筛选按钮 - 只在有筛选时显示 */}
                {selectedFolder() && (
                  <button
                    onClick={clearFolderFilter}
                    class="p-1 rounded hover:bg-gray-100 transition-colors"
                    title="清除文件夹筛选"
                  >
                    <X size={14} class="text-gray-500" />
                  </button>
                )}

                <button
                  onClick={() => handleCreateFolder()}
                  class="p-1 rounded hover:bg-gray-100"
                  title="新建文件夹"
                >
                  <Plus size={16} class="text-gray-600" />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 文件夹列表 */}
        <div class="flex-1 overflow-y-auto p-2">
          {props.collapsed ? (
            // 收起状态：只显示图标
            <div class="space-y-2">
              <button class="w-full p-3 rounded-lg hover:bg-theme-muted flex items-center justify-center">
                <Folder size={20} class="text-theme-secondary" />
              </button>
              <button class="w-full p-3 rounded-lg hover:bg-theme-muted flex items-center justify-center">
                <FileText size={20} class="text-theme-secondary" />
              </button>
            </div>
          ) : (
            // 展开状态：显示完整文件夹树
            <div class="space-y-1">
              {foldersLoading() ? (
                <div class="p-4 text-center text-theme-secondary">
                  <div class="animate-spin w-6 h-6 border-2 border-theme-primary-color border-t-transparent rounded-full mx-auto mb-2"></div>
                  加载中...
                </div>
              ) : (
                <div>
                  <For each={folderTree()}>
                    {(folder) => <FolderItem folder={folder} depth={0} />}
                  </For>
                </div>
              )}
            </div>
          )}
        </div>


      </div>

      {/* 新建文件夹对话框 */}
      <CreateFolderModal
        open={showCreateModal()}
        onClose={handleCloseCreateModal}
        onSuccess={handleCreateSuccess}
        parentPath={createParentPath()}
      />

      {/* 删除确认弹窗 */}
      <Modal
        open={showDeleteModal()}
        onClose={() => setShowDeleteModal(false)}
        title="删除文件夹"
        size="sm"
      >
        <div class="space-y-4">
          <p class="text-theme-secondary">
            确定要删除文件夹 <span class="font-medium text-theme-primary">"{selectedFolder()}"</span> 吗？
          </p>
          <p class="text-sm text-theme-error">
            此操作不可撤销，文件夹下的所有文献将被移动到根目录。
          </p>
          <div class="flex justify-end space-x-3 pt-4">
            <Button
              variant="secondary"
              onClick={() => setShowDeleteModal(false)}
            >
              取消
            </Button>
            <Button
              variant="danger"
              onClick={handleConfirmDelete}
            >
              确认删除
            </Button>
          </div>
        </div>
      </Modal>



      {/* 重命名文件夹对话框 */}
      <Modal
        open={showRenameModal()}
        onClose={handleCancelRename}
        title="重命名文件夹"
        size="sm"
      >
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-theme-primary mb-2">
              新文件夹名称
            </label>
            <input
              type="text"
              value={newFolderName()}
              onInput={(e) => setNewFolderName(e.target.value)}
              class="input-theme w-full px-3 py-2 border rounded-lg focus:ring-2"
              placeholder="请输入新的文件夹名称"
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleConfirmRename();
                if (e.key === 'Escape') handleCancelRename();
              }}
              autofocus
            />
          </div>
          <div class="flex justify-end space-x-3 pt-4">
            <Button
              variant="secondary"
              onClick={handleCancelRename}
            >
              取消
            </Button>
            <Button
              variant="primary"
              onClick={handleConfirmRename}
              disabled={!newFolderName().trim()}
            >
              确认
            </Button>
          </div>
        </div>
      </Modal>
    </aside>
  );
};

export default Sidebar;
