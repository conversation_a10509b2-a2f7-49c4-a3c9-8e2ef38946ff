import { createSignal } from 'solid-js';
import { Folder } from 'lucide-solid';
import Modal from '../ui/Modal';
import { Button, Input, toast } from '../ui';
import { usePaperContext } from '../../stores/PaperContext';

const CreateFolderModal = (props) => {
  const { createFolder, foldersLoading } = usePaperContext();
  
  const [folderName, setFolderName] = createSignal('');
  const [error, setError] = createSignal('');
  const [isSubmitting, setIsSubmitting] = createSignal(false);

  // 验证文件夹名称
  const validateFolderName = (name) => {
    if (!name.trim()) {
      return '文件夹名称不能为空';
    }
    if (name.trim().length < 1) {
      return '文件夹名称至少需要1个字符';
    }
    if (name.trim().length > 50) {
      return '文件夹名称不能超过50个字符';
    }
    // 检查非法字符
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(name)) {
      return '文件夹名称不能包含以下字符: < > : " / \\ | ? *';
    }
    return '';
  };

  // 处理输入变化
  const handleInputChange = (e) => {
    const value = e.target.value;
    setFolderName(value);
    
    // 实时验证
    const validationError = validateFolderName(value);
    setError(validationError);
  };

  // 处理表单提交
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const name = folderName().trim();
    const validationError = validateFolderName(name);
    
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setIsSubmitting(true);
      setError('');
      
      // 创建文件夹
      await createFolder(name, props.parentPath || '');
      
      // 成功后关闭对话框并重置状态
      handleClose();

      // 显示成功提示
      toast.success('文件夹创建成功');

      // 通知父组件
      if (props.onSuccess) {
        props.onSuccess(name);
      }
    } catch (err) {
      console.error('创建文件夹失败:', err);
      setError(err.message || '创建文件夹失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 关闭对话框
  const handleClose = () => {
    if (isSubmitting()) return; // 提交中不允许关闭
    
    setFolderName('');
    setError('');
    setIsSubmitting(false);
    
    if (props.onClose) {
      props.onClose();
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !isSubmitting()) {
      handleSubmit(e);
    }
  };

  return (
    <Modal
      open={props.open}
      onClose={handleClose}
      title="新建文件夹"
      size="sm"
      disableBackdropClose={isSubmitting()}
      showCloseButton={!isSubmitting()}
    >
      <form onSubmit={handleSubmit} class="space-y-4">
        {/* 父文件夹显示 */}
        {props.parentPath && (
          <div class="flex items-center p-3 bg-theme-muted rounded-lg">
            <Folder size={16} class="text-theme-muted mr-2" />
            <span class="text-sm text-theme-secondary">
              父文件夹: <span class="font-medium text-theme-primary">{props.parentPath}</span>
            </span>
          </div>
        )}

        {/* 文件夹名称输入 */}
        <div class="space-y-2">
          <Input
            label="文件夹名称"
            value={folderName()}
            onInput={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="请输入文件夹名称"
            disabled={isSubmitting()}
            error={error()}
            helperText="文件夹名称长度为1-50个字符，不能包含特殊字符"
            leftIcon={<Folder size={16} />}
            required
            autofocus
          />
        </div>

        {/* 按钮组 */}
        <div class="flex items-center justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting()}
          >
            取消
          </Button>
          <Button
            type="submit"
            loading={isSubmitting() || foldersLoading()}
            disabled={!folderName().trim() || !!error() || isSubmitting()}
          >
            创建文件夹
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default CreateFolderModal;
