import { createSignal, Show, For, Index } from 'solid-js';
import { FileText, AlertCircle, Plus, X, Folder } from 'lucide-solid';
import Modal from '../ui/Modal';
import { Button, Input, Textarea, NumberInput, toast } from '../ui';
import { usePaperContext } from '../../stores/PaperContext';
import { formatFolderPath } from '../../utils/folderUtils';

const CreatePaperModal = (props) => {
  const { createPaper, filters } = usePaperContext();

  // 表单状态
  const [formData, setFormData] = createSignal({
    title: '',
    authors: [], // 改为空数组，用于存储已添加的作者标签
    journal: '',
    year: new Date().getFullYear(),
    doi: '',
    abstract_text: '',
    keywords: [] // 改为空数组，用于存储已添加的关键词标签
    // 移除 folder_path，直接在提交时使用当前的 filters().folder
  });

  // 输入框状态
  const [authorInput, setAuthorInput] = createSignal('');
  const [keywordInput, setKeywordInput] = createSignal('');
  
  const [errors, setErrors] = createSignal({});
  const [isSubmitting, setIsSubmitting] = createSignal(false);

  // 验证表单
  const validateForm = () => {
    const data = formData();
    const newErrors = {};

    // 必填字段验证
    if (!data.title.trim()) {
      newErrors.title = '标题不能为空';
    }

    if (data.authors.length === 0) {
      newErrors.authors = '至少需要一个作者';
    }

    if (!data.journal.trim()) {
      newErrors.journal = '期刊名称不能为空';
    }

    if (!data.year || data.year < 1900 || data.year > new Date().getFullYear() + 10) {
      newErrors.year = '请输入有效的年份';
    }

    // DOI格式验证（可选）
    if (data.doi) {
      const doiTrimmed = data.doi.trim();
      // DOI验证：检查是否包含10.前缀，后面跟数字和斜杠，然后是任意字符
      // 支持格式如：10.1000/182, 10.1038/nature12373, 10.1016/j.cell.2019.05.031
      if (doiTrimmed && !/10\.\d+\/\S+/.test(doiTrimmed)) {
        newErrors.doi = 'DOI格式不正确（应为10.xxxx/xxxx格式）';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 更新表单数据
  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除对应字段的错误
    if (errors()[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // 添加作者标签
  const addAuthor = () => {
    const authorName = authorInput().trim();
    if (!authorName) return;

    // 检查是否已存在相同作者
    if (formData().authors.includes(authorName)) {
      toast.error('该作者已存在');
      return;
    }

    setFormData(prev => ({
      ...prev,
      authors: [...prev.authors, authorName]
    }));

    // 清空输入框
    setAuthorInput('');

    // 清除作者字段的错误
    if (errors().authors) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.authors;
        return newErrors;
      });
    }
  };

  // 移除作者标签
  const removeAuthor = (authorName) => {
    setFormData(prev => ({
      ...prev,
      authors: prev.authors.filter(author => author !== authorName)
    }));
  };

  // 处理作者输入框回车事件
  const handleAuthorKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addAuthor();
    }
  };

  // 添加关键词标签
  const addKeyword = () => {
    const keywordText = keywordInput().trim();
    if (!keywordText) return;

    // 检查是否已存在相同关键词
    if (formData().keywords.includes(keywordText)) {
      toast.error('该关键词已存在');
      return;
    }

    setFormData(prev => ({
      ...prev,
      keywords: [...prev.keywords, keywordText]
    }));

    // 清空输入框
    setKeywordInput('');

    // 清除关键词字段的错误
    if (errors().keywords) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.keywords;
        return newErrors;
      });
    }
  };

  // 移除关键词标签
  const removeKeyword = (keywordText) => {
    setFormData(prev => ({
      ...prev,
      keywords: prev.keywords.filter(keyword => keyword !== keywordText)
    }));
  };

  // 处理关键词输入框回车事件
  const handleKeywordKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addKeyword();
    }
  };



  // 提交表单
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      
      // 清理数据
      const data = formData();
      const cleanData = {
        title: data.title.trim(),
        authors: data.authors, // 作者已经是字符串数组，无需额外处理
        journal: data.journal.trim(),
        year: parseInt(data.year),
        doi: data.doi.trim() || undefined,
        abstract_text: data.abstract_text.trim() || undefined,
        keywords: data.keywords, // 关键词已经是字符串数组，无需额外处理
        folder_path: filters().folder || undefined // 直接使用当前的 filters().folder
      };

      // 创建文献
      const newPaper = await createPaper(cleanData);
      
      // 成功提示
      toast.success('文献创建成功');
      
      // 关闭对话框
      handleClose();
      
      // 通知父组件
      if (props.onSuccess) {
        props.onSuccess(newPaper);
      }
    } catch (err) {
      console.error('创建文献失败:', err);
      toast.error(err.message || '创建文献失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 关闭对话框
  const handleClose = () => {
    if (isSubmitting()) return;
    
    // 重置表单
    setFormData({
      title: '',
      authors: [], // 重置为空数组
      journal: '',
      year: new Date().getFullYear(),
      doi: '',
      abstract_text: '',
      keywords: [] // 重置为空数组
      // 移除 folder_path，直接使用当前的 filters().folder
    });
    setAuthorInput(''); // 重置作者输入框
    setKeywordInput(''); // 重置关键词输入框
    setErrors({});
    setIsSubmitting(false);
    
    if (props.onClose) {
      props.onClose();
    }
  };

  return (
    <Modal
      open={props.open}
      onClose={handleClose}
      title="添加文献"
      size="lg"
      disableBackdropClose={isSubmitting()}
      showCloseButton={!isSubmitting()}
    >
      <div class="space-y-6">
        {/* 标题 */}
        <div>
          <label class="block text-sm font-medium text-theme-primary mb-2">
            标题 <span class="text-theme-error">*</span>
          </label>
          <Input
            value={formData().title}
            onInput={(e) => updateFormData('title', e.target.value)}
            placeholder="请输入文献标题"
            disabled={isSubmitting()}
            variant={errors().title ? 'error' : 'default'}
          />
          <Show when={errors().title}>
            <div class="flex items-center text-sm text-theme-error mt-1">
              <AlertCircle size={14} class="mr-1" />
              {errors().title}
            </div>
          </Show>
        </div>

        {/* 作者 */}
        <div>
          <label class="block text-sm font-medium text-theme-primary mb-2">
            作者 <span class="text-theme-error">*</span>
          </label>

          {/* 作者输入框 */}
          <div class="flex items-center space-x-2 mb-3">
            <Input
              value={authorInput()}
              onInput={(e) => setAuthorInput(e.target.value)}
              onKeyPress={handleAuthorKeyPress}
              placeholder="输入作者姓名"
              disabled={isSubmitting()}
              class="flex-1"
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addAuthor}
              disabled={isSubmitting() || !authorInput().trim()}
            >
              <Plus size={16} class="mr-1" />
              添加
            </Button>
          </div>

          {/* 作者标签列表 */}
          <Show when={formData().authors.length > 0}>
            <div class="flex flex-wrap gap-2 p-3 bg-theme-muted rounded-lg border border-theme-primary min-h-[60px]">
              <For each={formData().authors}>
                {(author) => (
                  <div class="inline-flex items-center px-3 py-1 bg-theme-elevated border border-theme-secondary rounded-full text-sm">
                    <span class="text-theme-primary">{author}</span>
                    <button
                      type="button"
                      onClick={() => removeAuthor(author)}
                      disabled={isSubmitting()}
                      class="ml-2 text-theme-muted hover:text-theme-error focus:outline-none disabled:opacity-50"
                    >
                      <X size={14} />
                    </button>
                  </div>
                )}
              </For>
            </div>
          </Show>

          {/* 空状态提示 */}
          <Show when={formData().authors.length === 0}>
            <div class="p-3 bg-theme-muted rounded-lg border border-theme-primary min-h-[60px] flex items-center justify-center">
              <span class="text-theme-muted text-sm">暂无作者，请在上方输入框中添加</span>
            </div>
          </Show>

          <Show when={errors().authors}>
            <div class="flex items-center text-sm text-theme-error mt-2">
              <AlertCircle size={14} class="mr-1" />
              {errors().authors}
            </div>
          </Show>
        </div>

        {/* 期刊和年份 */}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-theme-primary mb-2">
              期刊 <span class="text-theme-error">*</span>
            </label>
            <Input
              value={formData().journal}
              onInput={(e) => updateFormData('journal', e.target.value)}
              placeholder="请输入期刊名称"
              disabled={isSubmitting()}
              variant={errors().journal ? 'error' : 'default'}
            />
            <Show when={errors().journal}>
              <div class="flex items-center text-sm text-theme-error mt-1">
                <AlertCircle size={14} class="mr-1" />
                {errors().journal}
              </div>
            </Show>
          </div>

          <div>
            <label class="block text-sm font-medium text-theme-primary mb-2">
              年份 <span class="text-theme-error">*</span>
            </label>
            <NumberInput
              value={formData().year}
              onValueChange={(value) => updateFormData('year', value)}
              placeholder="发表年份"
              disabled={isSubmitting()}
              variant={errors().year ? 'error' : 'default'}
              min={1900}
              max={new Date().getFullYear() + 10}
              step={1}
              allowEmpty={false}
            />
            <Show when={errors().year}>
              <div class="flex items-center text-sm text-theme-error mt-1">
                <AlertCircle size={14} class="mr-1" />
                {errors().year}
              </div>
            </Show>
          </div>
        </div>

        {/* DOI */}
        <div>
          <label class="block text-sm font-medium text-theme-primary mb-2">
            DOI（可选）
          </label>
          <Input
            value={formData().doi}
            onInput={(e) => updateFormData('doi', e.target.value)}
            placeholder="例如：10.1000/182 或 10.1038/nature12373"
            disabled={isSubmitting()}
            variant={errors().doi ? 'error' : 'default'}
          />
          <Show when={errors().doi}>
            <div class="flex items-center text-sm text-theme-error mt-1">
              <AlertCircle size={14} class="mr-1" />
              {errors().doi}
            </div>
          </Show>
        </div>

        {/* 关键词 */}
        <div>
          <label class="block text-sm font-medium text-theme-primary mb-2">
            关键词
          </label>

          {/* 关键词输入框 */}
          <div class="flex items-center space-x-2 mb-3">
            <Input
              value={keywordInput()}
              onInput={(e) => setKeywordInput(e.target.value)}
              onKeyPress={handleKeywordKeyPress}
              placeholder="输入关键词"
              disabled={isSubmitting()}
              class="flex-1"
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addKeyword}
              disabled={isSubmitting() || !keywordInput().trim()}
            >
              <Plus size={16} class="mr-1" />
              添加
            </Button>
          </div>

          {/* 关键词标签列表 */}
          <Show when={formData().keywords.length > 0}>
            <div class="flex flex-wrap gap-2 p-3 bg-theme-muted rounded-lg border border-theme-primary min-h-[60px]">
              <For each={formData().keywords}>
                {(keyword) => (
                  <div class="inline-flex items-center px-3 py-1 bg-theme-elevated border border-theme-secondary rounded-full text-sm">
                    <span class="text-theme-primary">{keyword}</span>
                    <button
                      type="button"
                      onClick={() => removeKeyword(keyword)}
                      disabled={isSubmitting()}
                      class="ml-2 text-theme-muted hover:text-theme-error focus:outline-none disabled:opacity-50"
                    >
                      <X size={14} />
                    </button>
                  </div>
                )}
              </For>
            </div>
          </Show>

          {/* 空状态提示 */}
          <Show when={formData().keywords.length === 0}>
            <div class="p-3 bg-theme-muted rounded-lg border border-theme-primary min-h-[60px] flex items-center justify-center">
              <span class="text-theme-muted text-sm">暂无关键词，请在上方输入框中添加</span>
            </div>
          </Show>

          <Show when={errors().keywords}>
            <div class="flex items-center text-sm text-theme-error mt-2">
              <AlertCircle size={14} class="mr-1" />
              {errors().keywords}
            </div>
          </Show>
        </div>

        {/* 目标文件夹信息 */}
        <div class="bg-theme-muted p-4 rounded-lg border border-theme-primary">
          <div class="flex items-center space-x-2">
            <Folder size={16} class="text-theme-muted" />
            <span class="text-sm font-medium text-theme-secondary">文献将添加到：</span>
            <span class="text-sm text-theme-primary font-medium">
              {formatFolderPath(filters().folder)}
            </span>
          </div>
          <p class="text-xs text-theme-muted mt-1">
            如需更改文件夹，请先在侧边栏选择目标文件夹
          </p>
        </div>

        {/* 摘要 */}
        <div>
          <Textarea
            label="摘要（可选）"
            value={formData().abstract_text}
            onInput={(e) => updateFormData('abstract_text', e.target.value)}
            placeholder="请输入文献摘要..."
            disabled={isSubmitting()}
            rows="4"
            resize="none"
          />
        </div>

        {/* 按钮组 */}
        <div class="flex items-center justify-end space-x-3 pt-4 border-t border-theme-primary">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting()}
          >
            取消
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            loading={isSubmitting()}
            disabled={isSubmitting()}
          >
            创建文献
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default CreatePaperModal;
