const PaperBatchActions = (props) => {
  if (props.selectedCount === 0) {
    return null;
  }

  return (
    <div class="bg-theme-primary border-b border-theme-primary px-6 py-2">
      <div class="flex items-center justify-between">
        <span class="text-sm text-theme-secondary">
          已选择 {props.selectedCount} 个文献
        </span>
        <button
          onClick={props.onClearSelection}
          class="text-sm text-theme-muted hover:text-theme-primary"
        >
          取消选择
        </button>
      </div>
    </div>
  );
};

export default PaperBatchActions;
