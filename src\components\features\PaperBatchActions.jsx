import { FolderOpen, Trash2 } from 'lucide-solid';
import { Button } from '../ui';

const PaperBatchActions = (props) => {
  // 只有在选中文献时才显示批量操作栏
  if (props.selectedCount === 0) {
    return null;
  }

  return (
    <div class="bg-theme-primary border-b border-theme-primary px-6 py-3">
      <div class="flex items-center justify-between">
        <span class="text-sm text-theme-secondary">
          已选择 {props.selectedCount} 个文献
        </span>

        <div class="flex items-center space-x-3">
          {/* 移动按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={props.onMove}
            disabled={props.selectedCount === 0}
          >
            <FolderOpen size={14} class="mr-1" />
            移动到文件夹
          </Button>

          {/* 删除按钮 */}
          <Button
            variant="danger"
            size="sm"
            onClick={props.onDelete}
            disabled={props.selectedCount === 0}
          >
            <Trash2 size={14} class="mr-1" />
            删除
          </Button>

          {/* 取消选择 */}
          <button
            onClick={props.onClearSelection}
            class="text-sm text-theme-muted hover:text-theme-primary"
          >
            取消选择
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaperBatchActions;
