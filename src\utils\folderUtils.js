/**
 * 文件夹相关工具函数
 */

/**
 * 格式化文件夹路径显示
 * @param {string} folderPath - 文件夹路径
 * @returns {string} 格式化后的显示文本
 */
export const formatFolderPath = (folderPath) => {
  // 如果路径为空、null、undefined 或只包含空白字符，显示为根目录
  if (!folderPath || !folderPath.trim()) {
    return '根目录';
  }
  
  return folderPath.trim();
};

/**
 * 获取文件夹显示名称（用于面包屑等场景）
 * @param {string} folderPath - 文件夹路径
 * @returns {string} 文件夹名称
 */
export const getFolderDisplayName = (folderPath) => {
  const formatted = formatFolderPath(folderPath);
  
  // 如果是根目录，直接返回
  if (formatted === '根目录') {
    return formatted;
  }
  
  // 返回路径的最后一部分作为文件夹名称
  const parts = formatted.split('/');
  return parts[parts.length - 1] || '根目录';
};

/**
 * 检查是否为根目录
 * @param {string} folderPath - 文件夹路径
 * @returns {boolean} 是否为根目录
 */
export const isRootFolder = (folderPath) => {
  return !folderPath || !folderPath.trim();
};

/**
 * 获取父文件夹路径
 * @param {string} folderPath - 文件夹路径
 * @returns {string} 父文件夹路径
 */
export const getParentFolderPath = (folderPath) => {
  if (isRootFolder(folderPath)) {
    return '';
  }
  
  const parts = folderPath.trim().split('/');
  if (parts.length <= 1) {
    return '';
  }
  
  return parts.slice(0, -1).join('/');
};

/**
 * 构建文件夹路径
 * @param {string} parentPath - 父文件夹路径
 * @param {string} folderName - 文件夹名称
 * @returns {string} 完整的文件夹路径
 */
export const buildFolderPath = (parentPath, folderName) => {
  if (!folderName || !folderName.trim()) {
    return parentPath || '';
  }
  
  const cleanFolderName = folderName.trim();
  
  if (isRootFolder(parentPath)) {
    return cleanFolderName;
  }
  
  return `${parentPath.trim()}/${cleanFolderName}`;
};
