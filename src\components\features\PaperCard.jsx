import { For } from 'solid-js';
import {
  Calendar,
  User,
  BookOpen,
  Tag,
  Folder
} from 'lucide-solid';
import { HighlightText, Badge } from '../ui';
import { usePaperContext } from '../../stores/PaperContext';
import { formatFolderPath } from '../../utils/folderUtils';

const PaperCard = (props) => {
  // 获取搜索查询用于高亮
  const { searchQuery } = usePaperContext();

  const handleClick = () => {
    if (props.selectMode) {
      props.onToggleSelection(props.paper.id);
    } else {
      props.onSelect(props.paper);
    }
  };

  return (
    <div
      class={`relative bg-theme-primary rounded-xl border border-theme-primary p-5 hover:shadow-theme-xl hover:border-theme-accent transition-all duration-300 cursor-pointer group ${
        props.isSelected ? 'ring-2 ring-theme-accent bg-theme-muted border-theme-accent' : ''
      }`}
      onClick={handleClick}
    >
      {/* 选择框 - 右上角 */}
      {props.selectMode && (
        <div class="absolute top-3 right-3 z-10">
          <div class={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 shadow-theme-sm ${
            props.isSelected
              ? 'bg-theme-accent border-theme-accent'
              : 'bg-theme-primary border-theme-secondary hover:border-theme-accent'
          }`}>
            {props.isSelected && (
              <svg class="w-3.5 h-3.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            )}
          </div>
        </div>
      )}

      {/* 悬停时的操作按钮 */}
      {!props.selectMode && (
        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            class="p-2 bg-theme-primary rounded-lg shadow-theme-md border border-theme-secondary hover:bg-theme-muted transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              props.onSelect(props.paper);
            }}
          >
            <BookOpen size={14} class="text-theme-secondary" />
          </button>
        </div>
      )}

      {/* 文献标题 */}
      <h3 class={`text-lg font-bold text-theme-primary mb-3 leading-tight group-hover:text-theme-accent transition-colors ${props.selectMode ? 'pr-10' : 'pr-8'}`}>
        <HighlightText
          text={props.paper.title}
          searchQuery={searchQuery()}
          highlightClass="bg-yellow-200 text-yellow-900 px-1 py-0.5 rounded font-bold"
        />
      </h3>

      {/* 期刊和年份信息 */}
      <div class="flex items-center justify-between mb-3 text-sm">
        <div class="flex items-center text-theme-secondary">
          <BookOpen size={14} class="mr-2 text-theme-muted" />
          <span class="font-medium">{props.paper.journal}</span>
        </div>
        <div class="text-xs font-semibold text-theme-secondary bg-theme-muted px-2 py-1 rounded-md">
          {props.paper.year}
        </div>
      </div>

      {/* 作者信息 */}
      <div class="flex items-center mb-3 text-sm text-theme-secondary">
        <User size={14} class="mr-2 text-theme-muted flex-shrink-0" />
        <span class="line-clamp-1">{props.paper.authors.join(', ')}</span>
      </div>

      {/* 摘要 */}
      {props.paper.abstract_text && (
        <div class="mb-3">
          <p class="text-sm text-theme-secondary leading-relaxed line-clamp-3">
            <HighlightText
              text={props.paper.abstract_text}
              searchQuery={searchQuery()}
              highlightClass="bg-yellow-200 text-yellow-900 px-1 py-0.5 rounded"
            />
          </p>
        </div>
      )}

      {/* 关键词标签 */}
      {props.paper.keywords && props.paper.keywords.length > 0 && (
        <div class="flex flex-wrap gap-1.5 mb-3">
          <For each={props.paper.keywords.slice(0, 4)}>
            {(keyword) => (
              <Badge variant="secondary" size="sm">
                <Tag size={10} class="mr-1" />
                <HighlightText
                  text={keyword}
                  searchQuery={searchQuery()}
                  highlightClass="bg-yellow-300 text-yellow-900 px-0.5 py-0.5 rounded font-medium"
                />
              </Badge>
            )}
          </For>
          {props.paper.keywords.length > 4 && (
            <Badge variant="outline" size="sm">
              +{props.paper.keywords.length - 4}
            </Badge>
          )}
        </div>
      )}

      {/* 底部信息栏 */}
      <div class="flex items-center justify-between pt-3 border-t border-theme-muted">
        <div class="flex items-center text-xs text-theme-muted">
          <Folder size={12} class="mr-1" />
          <span class="truncate max-w-24">{formatFolderPath(props.paper.folder_path)}</span>
        </div>
        <div class="flex items-center text-xs text-theme-muted">
          <Calendar size={12} class="mr-1" />
          <span>{new Date(props.paper.created_at).toLocaleDateString()}</span>
        </div>
      </div>
    </div>
  );
};

export default PaperCard;
